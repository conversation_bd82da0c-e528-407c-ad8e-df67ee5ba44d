import Foundation

// MARK: - Phase 1 Models (Pantry-only)

enum MealType: String, Codable, CaseIterable, Identifiable {
    case breakfast
    case lunch
    case dinner
    
    var id: String { rawValue }
    var displayName: String {
        switch self {
        case .breakfast: return "Breakfast"
        case .lunch: return "Lunch"
        case .dinner: return "Dinner"
        }
    }
}

struct RecipeGenerationRequest: Codable {
    enum Mode: String, Codable { case quick, mealPrep }
    
    let mode: Mode
    let pantryContext: PantryContext
    let requestDetails: RequestDetails
    
    struct PantryContext: Codable {
        let hasItems: Bool
        let itemCount: Int
    }
    
    struct RequestDetails: Codable {
        let schedule: Schedule
        let meals: [MealRequest]
        let preferences: Preferences
        
        struct Schedule: Codable {
            let startDate: String // YYYY-MM-DD
            let days: Int
        }
        
        struct MealRequest: Codable {
            let type: MealType
            let numberOfDishes: Int
            let maxCookingTimeMinutes: Int
        }
        
        struct Preferences: Codable {
            let cuisines: [String]
            let additionalRequest: String?
            let equipmentOwned: [String]
        }
    }
}

