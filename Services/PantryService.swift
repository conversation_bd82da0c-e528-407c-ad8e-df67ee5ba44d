import Foundation
import SwiftUI

@MainActor
class PantryService: ObservableObject {
    @Published var pantryItems: [Ingredient] = []

    func addIngredients(_ newIngredients: [Ingredient]) {
        // Add only unique ingredients based on name
        for ingredient in newIngredients {
            if !pantryItems.contains(where: { $0.name.lowercased() == ingredient.name.lowercased() }) {
                pantryItems.append(ingredient)
            }
        }
    }

    func deletePantryItem(at offsets: IndexSet) {
        pantryItems.remove(atOffsets: offsets)
    }

    /// Delete pantry items that match the provided set of IDs
    func deletePantryItems(withIDs ids: Set<UUID>) {
        guard !ids.isEmpty else { return }
        pantryItems.removeAll { ids.contains($0.id) }
    }
}