import Foundation

// Builds RecipeGenerationRequest for Quick and Meal Prep modes
struct RecipeRequestBuilder {
    func buildQuickRequest(
        pantryItemCount: Int,
        mealType: MealType,
        numberOfDishes: Int,
        totalTimeMinutes: Int,
        cuisines: [String],
        additionalRequest: String?
    ) -> RecipeGenerationRequest {
        let today = ISO8601DateFormatter().string(from: Date())
        let startDate = String(today.prefix(10)) // YYYY-MM-DD
        let details = RecipeGenerationRequest.RequestDetails(
            schedule: .init(startDate: startDate, days: 1),
            meals: [
                .init(type: mealType, numberOfDishes: numberOfDishes, maxCookingTimeMinutes: totalTimeMinutes)
            ],
            preferences: .init(cuisines: cuisines, additionalRequest: additionalRequest, equipmentOwned: [])
        )
        return RecipeGenerationRequest(
            mode: .quick,
            pantryContext: .init(hasItems: pantryItemCount > 0, itemCount: pantryItemCount),
            requestDetails: details
        )
    }
}

