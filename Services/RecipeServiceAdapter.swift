import Foundation

// Adapts app inputs to Gemini prompt enforcing pantry-only and profile constraints
actor <PERSON><PERSON>peServiceAdapter {
    private let geminiService = GeminiAPIService()
    
    func generateQuickRecipes(request: RecipeGenerationRequest, pantryItems: [String]) async throws -> [Recipe] {
        precondition(request.mode == .quick)
        // Build a pantry-only prompt
        let prompt = buildPantryOnlyPrompt(request: request, pantryItems: pantryItems)
        let json = try await geminiService.processText(prompt)
        // For Phase 1, we keep a simple decoder compatible with single or multiple recipes
        if let data = json.data(using: .utf8), let recipe = try? JSONDecoder().decode(Recipe.self, from: data) {
            return [recipe]
        }
        // Try array of Recipe
        guard let data = json.data(using: .utf8) else { throw RecipeGenerationError.invalidJSONResponse }
        return try JSONDecoder().decode([Recipe].self, from: data)
    }
    
    private func buildPantryOnlyPrompt(request: RecipeGenerationRequest, pantryItems: [String]) -> String {
        let ingredientsList = pantryItems.joined(separator: ", ")
        let meals = request.requestDetails.meals
        let prefs = request.requestDetails.preferences
        let cuisinesText = prefs.cuisines.isEmpty ? "any" : prefs.cuisines.joined(separator: ", ")
        let addReq = prefs.additionalRequest ?? ""
        let servings = 2 // Placeholder; will be wired to Profile in later tasks
        
        if request.mode == .quick, let meal = meals.first {
            let perDishBudget = max(5, meal.maxCookingTimeMinutes / max(1, meal.numberOfDishes))
            return """
            You are an expert culinary AI.
            Rules:
            - Use ONLY the pantry ingredients listed below plus water. Do not add any other ingredients. If not possible, say there are no valid recipes.
            - Mode: Quick. Generate \(meal.numberOfDishes) distinct \(meal.type.rawValue) dishes. Total combined cooking time across all dishes must be <= \(meal.maxCookingTimeMinutes) minutes. Soft per-dish time budget is about \(perDishBudget) minutes.
            - Respect cuisines (optional): \(cuisinesText).
            - Additional request (optional): \(addReq)
            - Servings per dish: \(servings)
            - Output MUST be valid JSON. Prefer an array of Recipe objects if multiple dishes.
            
            Pantry Ingredients: \(ingredientsList)
            
            JSON Schema for a Recipe object:
            {
              "recipeTitle": "String",
              "description": "String",
              "ingredients": ["String"],
              "instructions": ["String"],
              "nutrition": {"calories": "String", "protein": "String", "carbs": "String", "fat": "String"}
            }
            """
        }
        // Fallback
        return "Pantry-only quick generation request."
    }
}

