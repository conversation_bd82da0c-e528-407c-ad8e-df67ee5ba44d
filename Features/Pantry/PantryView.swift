import SwiftUI

struct PantryView: View {
    @StateObject private var viewModel = PantryViewModel()
    @EnvironmentObject var pantryService: PantryService
    @State private var editMode: EditMode = .inactive
    @State private var selectedIDs = Set<UUID>()
    @State private var showDeleteConfirm = false

    var body: some View {
        NavigationView {
            List {
                if pantryService.pantryItems.isEmpty {
                    Text("Your pantry is empty")
                        .foregroundColor(.secondary)
                        .italic()
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .center)
                } else {
                    ForEach(pantryService.pantryItems) { ingredient in
                        HStack(spacing: 12) {
                            if editMode == .active {
                                Image(systemName: selectedIDs.contains(ingredient.id) ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(selectedIDs.contains(ingredient.id) ? .blue : .secondary)
                            }
                            Text(ingredient.name)
                                .font(.body)
                            Spacer()
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            if editMode == .active {
                                toggleSelection(for: ingredient.id)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("Pantry")
            .listStyle(InsetGroupedListStyle())
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    EditButton()
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    if editMode == .active {
                        HStack(spacing: 12) {
                            Text("Selected: \(selectedIDs.count)")
                                .foregroundColor(.secondary)
                            Button(role: .destructive) {
                                showDeleteConfirm = true
                            } label: {
                                Text("Delete Selected")
                            }
                            .disabled(selectedIDs.isEmpty)
                        }
                    }
                }
            }
            .alert(
                Text("Delete \(selectedIDs.count) selected item(s)?"),
                isPresented: $showDeleteConfirm
            ) {
                Button("Delete", role: .destructive) {
                    let idsToDelete = selectedIDs.intersection(Set(pantryService.pantryItems.map { $0.id }))
                    pantryService.deletePantryItems(withIDs: idsToDelete)
                    selectedIDs.removeAll()
                }
                Button("Cancel", role: .cancel) { showDeleteConfirm = false }
            } message: {
                let names = pantryService.pantryItems.filter { selectedIDs.contains($0.id) }.map { $0.name }
                Text(names.isEmpty ? "No items selected." : names.joined(separator: ", "))
            }
            .onChange(of: editMode) { newValue in
                if newValue != .active { selectedIDs.removeAll() }
            }
            .environment(\.editMode, $editMode)
        }
    }

    private func toggleSelection(for id: UUID) {
        if selectedIDs.contains(id) {
            selectedIDs.remove(id)
        } else {
            selectedIDs.insert(id)
        }
    }
}