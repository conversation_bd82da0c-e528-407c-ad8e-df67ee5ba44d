import SwiftUI

struct RecipeGeneratorView: View {
    @StateObject private var viewModel: RecipeGeneratorViewModel
    @EnvironmentObject var pantryService: PantryService
    @State private var showingRecipeDetail = false

    // Adaptive grid for cuisine chips
    private let cuisineColumns = [GridItem(.adaptive(minimum: 110), spacing: 8)]

    init(pantryService: PantryService) {
        _viewModel = StateObject(wrappedValue: RecipeGeneratorViewModel(pantryService: pantryService))
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Pantry Items Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Your Pantry Items")
                            .font(.headline)
                        
                        if pantryService.pantryItems.isEmpty {
                            Text("Your pantry is empty. Add ingredients from the Scan tab first.")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(10)
                        } else {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 8) {
                                    ForEach(pantryService.pantryItems) { ingredient in
                                        Text(ingredient.name)
                                            .font(.caption)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(Color.blue.opacity(0.1))
                                            .foregroundColor(.blue)
                                            .cornerRadius(15)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    Divider()
                    
                    // Preferences Section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Recipe Preferences")
                            .font(.headline)
                        
                        // Meal Type
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Meal Type")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Picker("Meal Type", selection: $viewModel.mealType) {
                                ForEach(MealType.allCases) { type in
                                    Text(type.displayName).tag(type)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                        }

                        // Number of Dishes
                        HStack {
                            Text("Number of Dishes")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Spacer()
                            Stepper("\(viewModel.numberOfDishes)", value: $viewModel.numberOfDishes, in: 1...6)
                                .fixedSize()
                        }

                        // Total Time (minutes)
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Total Time (minutes)")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text("\(viewModel.totalTimeMinutes) min")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            Slider(value: Binding(
                                get: { Double(viewModel.totalTimeMinutes) },
                                set: { viewModel.totalTimeMinutes = Int($0.rounded() / 5) * 5 }
                            ), in: 5...120, step: 5)
                        }

                        // Cuisines (multi-select)
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Cuisines")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            LazyVGrid(columns: cuisineColumns, alignment: .leading, spacing: 8) {
                                ForEach(viewModel.allCuisines, id: \.self) { cuisine in
                                    let selected = viewModel.selectedCuisines.contains(cuisine)
                                    Text(cuisine)
                                        .font(.caption)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .background(selected ? Color.blue.opacity(0.2) : Color.gray.opacity(0.12))
                                        .foregroundColor(selected ? .blue : .primary)
                                        .cornerRadius(14)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 14)
                                                .stroke(selected ? Color.blue : Color.gray.opacity(0.4), lineWidth: 1)
                                        )
                                        .onTapGesture { viewModel.toggleCuisine(cuisine) }
                                }
                            }
                        }

                        // Additional Request
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Additional Request (optional)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            TextEditor(text: $viewModel.additionalRequest)
                                .frame(minHeight: 80)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }
                    .padding(.horizontal)
                    
                    // Generate Button
                    Button(action: {
                        Task {
                            await viewModel.generateRecipe()
                            if viewModel.generatedRecipe != nil {
                                showingRecipeDetail = true
                            }
                        }
                    }) {
                        HStack {
                            if viewModel.isGenerating {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "flame.fill")
                            }
                            Text(viewModel.isGenerating ? "Generating..." : "Generate Recipe")
                        }
                        .font(.title3.weight(.medium))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(viewModel.canGenerateRecipe ? Color.orange : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(!viewModel.canGenerateRecipe)
                    .padding(.horizontal)
                    .padding(.top, 10)
                }
                .padding(.vertical)
            }
            .navigationTitle("Recipe Generator")
            .navigationBarTitleDisplayMode(.large)
            .alert("Error", isPresented: $viewModel.showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(viewModel.errorMessage)
            }
            .sheet(isPresented: $showingRecipeDetail) {
                if let recipe = viewModel.generatedRecipe {
                    GeneratedRecipeDetailView(recipe: recipe)
                }
            }
        }
    }
} 