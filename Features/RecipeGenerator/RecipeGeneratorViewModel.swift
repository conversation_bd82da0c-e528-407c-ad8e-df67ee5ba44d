import Foundation
import SwiftUI

@MainActor
class RecipeGeneratorViewModel: ObservableObject {
    // Phase 1 Quick controls
    @Published var mealType: MealType = .dinner
    @Published var numberOfDishes: Int = 1 // 1...6
    @Published var totalTimeMinutes: Int = 30 // 5...120 step 5
    @Published var selectedCuisines = Set<String>()
    @Published var additionalRequest: String = ""

    @Published var isGenerating = false
    @Published var generatedRecipe: Recipe?
    @Published var showError = false
    @Published var errorMessage = ""

    private let pantryService: PantryService
    private let adapter = RecipeServiceAdapter()
    private let builder = RecipeRequestBuilder()

    // Static cuisines list per PRD (initial)
    let allCuisines: [String] = [
        "Chinese", "Taiwanese", "Japanese", "Korean", "Thai", "Vietnamese", "Indian",
        "Italian", "Mediterranean", "Mexican", "American", "French", "Spanish",
        "Middle Eastern", "Greek", "Latin American", "African"
    ]

    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }

    var canGenerateRecipe: Bool {
        !pantryService.pantryItems.isEmpty && !isGenerating && numberOfDishes >= 1 && numberOfDishes <= 6 && totalTimeMinutes >= 5 && totalTimeMinutes <= 120
    }

    func toggleCuisine(_ name: String) {
        if selectedCuisines.contains(name) { selectedCuisines.remove(name) } else { selectedCuisines.insert(name) }
    }

    func generateRecipe() async {
        guard canGenerateRecipe else { return }
        isGenerating = true
        showError = false

        let ingredients = pantryService.pantryItems.map { $0.name }
        let request = builder.buildQuickRequest(
            pantryItemCount: ingredients.count,
            mealType: mealType,
            numberOfDishes: numberOfDishes,
            totalTimeMinutes: totalTimeMinutes,
            cuisines: Array(selectedCuisines),
            additionalRequest: additionalRequest.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : additionalRequest
        )
        do {
            let recipes = try await adapter.generateQuickRecipes(request: request, pantryItems: ingredients)
            generatedRecipe = recipes.first
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        isGenerating = false
    }
}